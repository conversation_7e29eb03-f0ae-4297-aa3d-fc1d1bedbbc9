# QR Code 审计报告: ID 46 - Bintang Collectionz Hotel

**审计日期:** 2024-08-02
**审计员:** Gemini AI Assistant

---

## 1. 综合评估

- **项目ID:** 46
- **项目名称:** Bintang Collectionz Hotel
- **状态:** `<font color="red">翻译不完整 (Partial)</font>`
- **原因:** 此项目包含12个作为服务模板的子项目，根据旧报告分析，所有子项目均仅有英文翻译，状态为 1/10。

---

## 2. 子项目清单与状态

此项目总共包含 **12** 个翻译不完整的子项目。

| 子项目 ID | 子项目名称 | 服务类型 | 翻译状态 |
| :--- | :--- | :--- | :--- |
| 95 | Genting Highland Private Charter (10 Hours) | Private Charter | 🚨 1/10 |
| 94 | Kuala Selangor Private Charter (10 Hours) | Private Charter | 🚨 1/10 |
| 93 | Kuala Selangor Private Charter (6 Hours) | Private Charter | 🚨 1/10 |
| 92 | Melaka Private Tour (10 Hours) | Private Tour | 🚨 1/10 |
| 91 | Kuala Lumpur City Tour (10 Hours) | City Tour | 🚨 1/10 |
| 90 | Kuala Lumpur City Tour (5 Hours) | City Tour | 🚨 1/10 |
| 89 | Airport transfer Pickup (Melaka) | Airport Transfer | 🚨 1/10 |
| 88 | Airport transfer Drop Off (Melaka) | Airport Transfer | 🚨 1/10 |
| 87 | Airport transfer Drop Off (Genting Highland) | Airport Transfer | 🚨 1/10 |
| 86 | Airport transfer Pickup (Genting Highland) | Airport Transfer | 🚨 1/10 |
| 85 | Airport transfer Pickup (Klang Valley) | Airport Transfer | 🚨 1/10 |
| 84 | Airport transfer Drop Off (Klang Valley) | Airport Transfer | 🚨 1/10 |

---

## 3. 历史记录

- **[2024-08-02]** - AI助手执行目录整改，遵循 `[ID]_[项目名称].md` 的命名规范，从旧格式报告 `Bintang_Collectionz_Hotel_ID_46_Report.md` 迁移数据，并创建此份标准化报告。 