<!DOCTYPE html>
<!--
    商务内容翻译优化工具 
    集成 Google Gemini AI API 实现专业翻译
    
    安全提示：此文件包含API密钥，仅用于演示目的
    在生产环境中，应将API密钥存储在服务器端以确保安全
-->
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商务内容翻译优化工具 - Powered by Gemini AI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(45deg, #4a90e2, #357abd);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .main-content {
            padding: 30px;
        }

        .input-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }

        .input-group {
            margin-bottom: 20px;
        }

        .input-group label {
            display: block;
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
        }

        .original-text {
            width: 100%;
            min-height: 150px;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 14px;
            line-height: 1.6;
            resize: vertical;
            transition: border-color 0.3s;
        }

        .original-text:focus {
            outline: none;
            border-color: #4a90e2;
        }

        .style-selector {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 12px;
            margin-bottom: 20px;
        }

        .style-option {
            padding: 12px;
            background: white;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            min-height: 85px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .style-option:hover {
            border-color: #4a90e2;
            transform: translateY(-2px);
        }

        .style-option.selected {
            border-color: #4a90e2;
            background: #e3f2fd;
        }

        /* 风格特色颜色 */
        .style-option[data-style="friendly"] {
            border-left: 4px solid #ff9800;
        }
        
        .style-option[data-style="friendly"]:hover,
        .style-option[data-style="friendly"].selected {
            border-color: #ff9800;
            background: #fff3e0;
        }

        .style-option[data-style="courteous"] {
            border-left: 4px solid #9c27b0;
        }
        
        .style-option[data-style="courteous"]:hover,
        .style-option[data-style="courteous"].selected {
            border-color: #9c27b0;
            background: #f3e5f5;
        }

        .style-option[data-style="marketing"] {
            border-left: 4px solid #e91e63;
        }
        
        .style-option[data-style="marketing"]:hover,
        .style-option[data-style="marketing"].selected {
            border-color: #e91e63;
            background: #fce4ec;
        }

        .style-option[data-style="caring"] {
            border-left: 4px solid #4caf50;
        }
        
        .style-option[data-style="caring"]:hover,
        .style-option[data-style="caring"].selected {
            border-color: #4caf50;
            background: #e8f5e8;
        }

        .style-option[data-style="gentle"] {
            border-left: 4px solid #00bcd4;
        }
        
        .style-option[data-style="gentle"]:hover,
        .style-option[data-style="gentle"].selected {
            border-color: #00bcd4;
            background: #e0f2f1;
        }

        .style-option[data-style="urgent"] {
            border-left: 4px solid #f44336;
        }
        
        .style-option[data-style="urgent"]:hover,
        .style-option[data-style="urgent"].selected {
            border-color: #f44336;
            background: #ffebee;
        }

        .style-option[data-style="strong"] {
            border-left: 4px solid #795548;
        }
        
        .style-option[data-style="strong"]:hover,
        .style-option[data-style="strong"].selected {
            border-color: #795548;
            background: #efebe9;
        }

        .style-option h3 {
            color: #333;
            margin-bottom: 5px;
        }

        .style-option p {
            color: #666;
            font-size: 0.9rem;
        }

        .language-selector {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-bottom: 20px;
        }

        .language-checkbox {
            display: flex;
            align-items: center;
            padding: 10px;
            background: white;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .language-checkbox:hover {
            background: #f0f8ff;
            border-color: #4a90e2;
        }

        .language-checkbox input[type="checkbox"] {
            margin-right: 10px;
            transform: scale(1.2);
        }

        .translate-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(45deg, #4a90e2, #357abd);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
        }

        .translate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(74, 144, 226, 0.3);
        }

        .translate-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .results-section {
            margin-top: 30px;
        }

        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
        }

        .result-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            border: 1px solid #e1e5e9;
            transition: all 0.3s ease;
        }

        .result-card.minimized {
            padding: 15px 20px;
        }

        .result-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .result-header .language-title {
            color: #333;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            cursor: pointer;
            user-select: none;
            transition: color 0.3s ease;
            margin: 0;
            font-weight: bold;
        }

        .result-header .language-title:hover {
            color: #4a90e2;
        }

        .expand-icon {
            margin-left: 10px;
            font-size: 0.8rem;
            color: #666;
            transition: transform 0.3s ease;
        }

        .result-card.minimized .expand-icon {
            transform: rotate(-90deg);
        }

        .result-content {
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .result-card.minimized .result-content {
            max-height: 0;
            margin-bottom: 0;
            opacity: 0;
        }

        .copy-btn {
            padding: 8px 15px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s;
        }

        .copy-btn:hover {
            background: #218838;
        }

        .copy-btn.copied {
            background: #6c757d;
        }

        .result-text {
            background: white;
            padding: 15px;
            border-radius: 8px;
            min-height: 100px;
            white-space: pre-wrap;
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            border: 1px solid #e1e5e9;
        }

        /* 格式化文本样式 */
        .result-text strong {
            font-weight: bold;
            color: #2c3e50;
            background: linear-gradient(120deg, #a8e6cf 0%, #dcedc8 100%);
            padding: 2px 4px;
            border-radius: 3px;
        }

        .result-text .underline {
            text-decoration: underline;
            text-decoration-color: #3498db;
            text-decoration-thickness: 2px;
            text-underline-offset: 2px;
        }

        .result-text .highlight {
            background: linear-gradient(120deg, #ffeaa7 0%, #fab1a0 100%);
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: 500;
        }

        .result-text .emoji {
            font-size: 1.1em;
            margin: 0 2px;
        }

        /* 不同风格的特色强调 */
        .result-text .urgent-highlight {
            background: linear-gradient(120deg, #ff7675 0%, #e84393 100%);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }

        .result-text .friendly-highlight {
            background: linear-gradient(120deg, #fdcb6e 0%, #e17055 100%);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 500;
        }

        .result-text .marketing-highlight {
            background: linear-gradient(120deg, #fd79a8 0%, #e84393 100%);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .spinner {
            display: inline-block;
            width: 30px;
            height: 30px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4a90e2;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .empty-state {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .empty-state svg {
            width: 80px;
            height: 80px;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        /* 历史记录相关样式 */
        .history-section {
            margin-top: 30px;
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
        }

        .history-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .history-header h2 {
            color: #333;
            font-size: 1.5rem;
        }

        .history-controls {
            display: flex;
            gap: 10px;
        }

        .history-btn {
            padding: 8px 15px;
            background: #6c757d;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s;
        }

        .history-btn:hover {
            background: #5a6268;
        }

        .history-btn.toggle {
            background: #4a90e2;
        }

        .history-btn.toggle:hover {
            background: #357abd;
        }

        .history-btn.clear {
            background: #dc3545;
        }

        .history-btn.clear:hover {
            background: #c82333;
        }

        .history-list {
            max-height: 400px;
            overflow-y: auto;
            border-radius: 10px;
        }

        .history-item {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            border: 1px solid #e1e5e9;
            transition: all 0.3s;
        }

        .history-item:hover {
            border-color: #4a90e2;
            transform: translateY(-1px);
        }

        .history-item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px;
        }

        .history-meta {
            font-size: 0.85rem;
            color: #666;
        }

        .history-actions {
            display: flex;
            gap: 5px;
        }

        .history-action-btn {
            padding: 4px 8px;
            font-size: 0.75rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .history-action-btn.restore {
            background: #28a745;
            color: white;
        }

        .history-action-btn.restore:hover {
            background: #218838;
        }

        .history-action-btn.delete {
            background: #dc3545;
            color: white;
        }

        .history-action-btn.delete:hover {
            background: #c82333;
        }

        .history-preview {
            font-size: 0.9rem;
            color: #333;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        /* 历史记录中的格式化文本样式 */
        .history-preview strong {
            font-weight: bold;
            color: #2c3e50;
            background: linear-gradient(120deg, #a8e6cf 0%, #dcedc8 100%);
            padding: 1px 2px;
            border-radius: 2px;
            font-size: 0.85rem;
        }

        .history-preview .underline {
            text-decoration: underline;
            text-decoration-color: #3498db;
            text-decoration-thickness: 1px;
        }

        .history-preview .highlight {
            background: linear-gradient(120deg, #ffeaa7 0%, #fab1a0 100%);
            padding: 1px 2px;
            border-radius: 2px;
            font-size: 0.85rem;
        }

        .hidden {
            display: none;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }

            .header {
                padding: 20px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .main-content {
                padding: 20px;
            }

            .style-selector {
                grid-template-columns: repeat(2, 1fr);
                gap: 8px;
            }

            .style-option {
                padding: 10px;
                min-height: 75px;
            }

            .style-option h3 {
                font-size: 0.9rem;
            }

            .style-option p {
                font-size: 0.8rem;
            }

            .results-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题区域 -->
        <div class="header">
            <h1>商务内容翻译优化工具</h1>
            <p>基于 Google Gemini AI 的专业翻译平台，为商务服务内容提供高质量多语言翻译</p>
        </div>

        <div class="main-content">
            <!-- 输入区域 -->
            <div class="input-section">
                <div class="input-group">
                    <label for="originalText">原始内容输入</label>
                    <textarea 
                        id="originalText" 
                        class="original-text" 
                        placeholder="请输入需要翻译和优化的商务内容，例如：预定通知、服务政策、操作说明等...">
                    </textarea>
                </div>

                <div class="input-group">
                    <label>选择优化风格</label>
                    <div class="style-selector">
                        <div class="style-option" data-style="formal">
                            <h3>正式风格</h3>
                            <p>适合官方公告、政策文件</p>
                        </div>
                        <div class="style-option selected" data-style="concise">
                            <h3>简洁风格</h3>
                            <p>适合快速阅读、要点说明</p>
                        </div>
                        <div class="style-option" data-style="detailed">
                            <h3>详细风格</h3>
                            <p>适合操作指南、详细说明</p>
                        </div>
                        <div class="style-option" data-style="friendly">
                            <h3>友好+美观</h3>
                            <p>温暖亲切、拉近距离</p>
                        </div>
                        <div class="style-option" data-style="courteous">
                            <h3>礼貌优雅+美观</h3>
                            <p>高度礼貌、优雅得体</p>
                        </div>
                        <div class="style-option" data-style="marketing">
                            <h3>营销推广+美观</h3>
                            <p>吸引眼球、说服力强</p>
                        </div>
                        <div class="style-option" data-style="caring">
                            <h3>温情关怀+美观</h3>
                            <p>体现关怀、情感温暖</p>
                        </div>
                        <div class="style-option" data-style="gentle">
                            <h3>温和安抚</h3>
                            <p>舒缓语气、减缓紧张</p>
                        </div>
                        <div class="style-option" data-style="urgent">
                            <h3>紧急提醒</h3>
                            <p>突出紧迫性、行动导向</p>
                        </div>
                        <div class="style-option" data-style="strong">
                            <h3>强烈措辞</h3>
                            <p>语气坚定、态度明确</p>
                        </div>
                    </div>
                </div>

                <div class="input-group">
                    <label>选择目标语言</label>
                    <div class="language-selector">
                        <label class="language-checkbox">
                            <input type="checkbox" value="zh-cn" checked>
                            <span>简体中文 (Simplified Chinese)</span>
                        </label>
                        <label class="language-checkbox">
                            <input type="checkbox" value="zh-tw">
                            <span>繁体中文 (Traditional Chinese)</span>
                        </label>
                        <label class="language-checkbox">
                            <input type="checkbox" value="en" checked>
                            <span>英语 (English)</span>
                        </label>
                        <label class="language-checkbox">
                            <input type="checkbox" value="ms">
                            <span>马来语 (Malay)</span>
                        </label>
                        <label class="language-checkbox">
                            <input type="checkbox" value="id">
                            <span>印尼语 (Indonesian)</span>
                        </label>
                        <label class="language-checkbox">
                            <input type="checkbox" value="ja">
                            <span>日语 (Japanese)</span>
                        </label>
                        <label class="language-checkbox">
                            <input type="checkbox" value="ko">
                            <span>韩语 (Korean)</span>
                        </label>
                        <label class="language-checkbox">
                            <input type="checkbox" value="th">
                            <span>泰语 (Thai)</span>
                        </label>
                        <label class="language-checkbox">
                            <input type="checkbox" value="vi">
                            <span>越南语 (Vietnamese)</span>
                        </label>
                        <label class="language-checkbox">
                            <input type="checkbox" value="ru">
                            <span>俄语 (Russian)</span>
                        </label>
                    </div>
                </div>

                <button class="translate-btn" id="translateBtn">开始翻译和优化</button>
            </div>

            <!-- 结果显示区域 -->
            <div class="results-section" id="resultsSection">
                <div class="empty-state">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.01-4.65.51-6.83l-1.97 1.97-.35-.35 3.54-3.54.35.35-1.97 1.97c2.52 1.82 2.68 5.25.59 7.28l-.03.03 2.51 2.54-.67.67zM8.13 8.93l2.54 2.51-.03.03c-1.74 1.94-2.01 4.65-.51 6.83l1.97-1.97.35.35-3.54 3.54-.35-.35 1.97-1.97c-2.52-1.82-2.68-5.25-.59-7.28l.03-.03-2.51-2.54.67-.67z"/>
                    </svg>
                    <h3>准备开始翻译</h3>
                    <p>请输入内容并选择目标语言，然后点击"开始翻译和优化"按钮</p>
                </div>
            </div>

            <!-- 历史记录区域 -->
            <div class="history-section" id="historySection">
                <div class="history-header">
                    <h2>翻译历史记录</h2>
                    <div class="history-controls">
                        <button class="history-btn toggle" id="toggleHistoryBtn">
                            显示历史记录
                        </button>
                        <button class="history-btn clear" id="clearHistoryBtn">
                            清空历史
                        </button>
                    </div>
                </div>
                <div class="history-list hidden" id="historyList">
                    <!-- 历史记录项目将动态插入这里 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        /**
         * @file 商务内容翻译优化工具的主要JavaScript逻辑
         * @function 实现风格选择、语言翻译和复制功能
         */

        // 语言配置映射
        const LANGUAGE_CONFIG = {
            'zh-cn': { name: '简体中文', code: 'zh-CN' },
            'zh-tw': { name: '繁体中文', code: 'zh-TW' },
            'en': { name: 'English', code: 'en' },
            'ms': { name: 'Bahasa Melayu', code: 'ms' },
            'id': { name: 'Bahasa Indonesia', code: 'id' },
            'ja': { name: '日本語', code: 'ja' },
            'ko': { name: '한국어', code: 'ko' },
            'th': { name: 'ภาษาไทย', code: 'th' },
            'vi': { name: 'Tiếng Việt', code: 'vi' },
            'ru': { name: 'Русский', code: 'ru' }
        };

        // 风格优化模板
        const STYLE_TEMPLATES = {
            formal: {
                name: '正式风格',
                transform: (text) => {
                    // 正式风格：增加礼貌用语，规范表达
                    return text.replace(/请/g, '敬请')
                              .replace(/谢谢/g, '衷心感谢')
                              .replace(/注意/g, '特别提醒');
                }
            },
            concise: {
                name: '简洁风格',
                transform: (text) => {
                    // 简洁风格：简化表达，去除冗余
                    return text.replace(/请您/g, '请')
                              .replace(/非常感谢/g, '谢谢')
                              .replace(/特别/g, '');
                }
            },
            detailed: {
                name: '详细风格',
                transform: (text) => {
                    // 详细风格：增加说明和补充信息
                    return text.replace(/请/g, '请您务必')
                              .replace(/注意/g, '请特别注意')
                              .replace(/完成/g, '完成相关操作');
                }
            },
            friendly: {
                name: '友好+美观',
                transform: (text) => {
                    // 友好风格：温暖亲切的表达
                    return text.replace(/您/g, '您')
                              .replace(/请/g, '请您')
                              .replace(/谢谢/g, '非常感谢您')
                              .replace(/欢迎/g, '热烈欢迎');
                }
            },
            courteous: {
                name: '礼貌优雅+美观',
                transform: (text) => {
                    // 礼貌优雅风格：高度礼貌的表达
                    return text.replace(/请/g, '恳请')
                              .replace(/谢谢/g, '深表感谢')
                              .replace(/不便/g, '带来的任何不便')
                              .replace(/您/g, '尊敬的您');
                }
            },
            marketing: {
                name: '营销推广+美观',
                transform: (text) => {
                    // 营销风格：吸引眼球、说服力强
                    return text.replace(/优质/g, '超值优质')
                              .replace(/服务/g, '专业服务')
                              .replace(/机会/g, '难得机会')
                              .replace(/选择/g, '明智选择');
                }
            },
            caring: {
                name: '温情关怀+美观',
                transform: (text) => {
                    // 温情关怀风格：体现关怀和温暖
                    return text.replace(/请/g, '请您放心')
                              .replace(/帮助/g, '贴心帮助')
                              .replace(/服务/g, '贴心服务')
                              .replace(/问题/g, '任何疑虑');
                }
            },
            gentle: {
                name: '温和安抚',
                transform: (text) => {
                    // 温和安抚风格：舒缓紧张情绪
                    return text.replace(/必须/g, '建议您')
                              .replace(/立即/g, '方便时')
                              .replace(/错误/g, '小问题')
                              .replace(/失败/g, '暂时未成功');
                }
            },
            urgent: {
                name: '紧急提醒',
                transform: (text) => {
                    // 紧急提醒风格：突出紧迫性
                    return text.replace(/请/g, '请立即')
                              .replace(/注意/g, '紧急注意')
                              .replace(/重要/g, '非常重要')
                              .replace(/尽快/g, '马上');
                }
            },
            strong: {
                name: '强烈措辞',
                transform: (text) => {
                    // 强烈措辞风格：语气坚定
                    return text.replace(/建议/g, '强烈建议')
                              .replace(/请/g, '务必')
                              .replace(/可能/g, '必然会')
                              .replace(/应该/g, '必须');
                }
            }
        };

        // 模拟翻译数据库（实际项目中应该使用真实的翻译API）
        const TRANSLATION_SAMPLES = {
            'zh-cn': {
                'booking_notice': '预订通知：您的预订已确认，请于指定时间到达。',
                'service_policy': '服务政策：我们致力于为您提供优质的服务体验。',
                'instructions': '操作说明：请按照以下步骤完成相关操作。'
            },
            'en': {
                'booking_notice': 'Booking Notice: Your reservation has been confirmed. Please arrive at the designated time.',
                'service_policy': 'Service Policy: We are committed to providing you with exceptional service experience.',
                'instructions': 'Instructions: Please follow the steps below to complete the required operations.'
            },
            'ja': {
                'booking_notice': '予約通知：ご予約が確認されました。指定時間にお越しください。',
                'service_policy': 'サービスポリシー：お客様に優質なサービス体験を提供することをお約束いたします。',
                'instructions': '操作説明：以下の手順に従って関連操作を完了してください。'
            },
            'ko': {
                'booking_notice': '예약 알림: 예약이 확인되었습니다. 지정된 시간에 도착해 주세요.',
                'service_policy': '서비스 정책: 고객님께 우수한 서비스 경험을 제공하기 위해 최선을 다하고 있습니다.',
                'instructions': '사용 설명서: 다음 단계에 따라 관련 작업을 완료하십시오.'
            }
        };

        let selectedStyle = 'concise';
        let isTranslating = false;
        let isHistoryVisible = false;

        // 历史记录存储键名
        const HISTORY_STORAGE_KEY = 'translation_history';

        /**
         * @function initializeApp - 初始化应用程序
         * @description 设置事件监听器和初始状态
         */
        function initializeApp() {
            setupStyleSelector();
            setupTranslateButton();
            setupLanguageCheckboxes();
            setupHistoryControls();
            loadTranslationHistory();
        }

        /**
         * @function setupStyleSelector - 设置风格选择器
         * @description 为风格选项添加点击事件监听器
         */
        function setupStyleSelector() {
            const styleOptions = document.querySelectorAll('.style-option');
            
            styleOptions.forEach(option => {
                option.addEventListener('click', () => {
                    // 移除所有选中状态
                    styleOptions.forEach(opt => opt.classList.remove('selected'));
                    // 添加当前选中状态
                    option.classList.add('selected');
                    selectedStyle = option.dataset.style;
                });
            });
        }

        /**
         * @function setupTranslateButton - 设置翻译按钮
         * @description 为翻译按钮添加点击事件监听器
         */
        function setupTranslateButton() {
            const translateBtn = document.getElementById('translateBtn');
            translateBtn.addEventListener('click', handleTranslate);
        }

        /**
         * @function setupLanguageCheckboxes - 设置语言复选框
         * @description 为语言复选框添加变化事件监听器
         */
        function setupLanguageCheckboxes() {
            const checkboxes = document.querySelectorAll('.language-checkbox input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', updateTranslateButtonState);
            });
        }

        /**
         * @function updateTranslateButtonState - 更新翻译按钮状态
         * @description 根据选择的语言数量启用或禁用翻译按钮
         */
        function updateTranslateButtonState() {
            const selectedLanguages = getSelectedLanguages();
            const translateBtn = document.getElementById('translateBtn');
            const originalText = document.getElementById('originalText').value.trim();
            
            translateBtn.disabled = selectedLanguages.length === 0 || originalText === '' || isTranslating;
        }

        /**
         * @function getSelectedLanguages - 获取选中的语言
         * @returns {Array} 选中的语言代码数组
         */
        function getSelectedLanguages() {
            const checkboxes = document.querySelectorAll('.language-checkbox input[type="checkbox"]:checked');
            return Array.from(checkboxes).map(cb => cb.value);
        }

        /**
         * @function handleTranslate - 处理翻译请求
         * @description 主要的翻译处理函数
         */
        async function handleTranslate() {
            const originalText = document.getElementById('originalText').value.trim();
            const selectedLanguages = getSelectedLanguages();

            if (!originalText || selectedLanguages.length === 0) {
                alert('请输入内容并选择至少一种目标语言');
                return;
            }

            isTranslating = true;
            updateTranslateButtonState();
            showLoadingState();

            try {
                // 首先应用风格优化
                const optimizedText = applyStyleOptimization(originalText, selectedStyle);
                
                // 执行翻译（使用Gemini API）
                const translations = await performTranslations(optimizedText, selectedLanguages);
                
                // 显示结果
                displayResults(translations);
                
            } catch (error) {
                console.error('翻译过程中发生错误:', error);
                alert('翻译过程中发生错误，请稍后重试');
            } finally {
                isTranslating = false;
                updateTranslateButtonState();
            }
        }

        /**
         * @function applyStyleOptimization - 应用风格优化
         * @param {string} text - 原始文本
         * @param {string} style - 选择的风格
         * @returns {string} 优化后的文本
         */
        function applyStyleOptimization(text, style) {
            const template = STYLE_TEMPLATES[style];
            if (template && template.transform) {
                return template.transform(text);
            }
            return text;
        }

        // Gemini API 配置
        const GEMINI_API_KEY = 'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s';
        const GEMINI_MODEL = 'gemini-2.5-flash-lite-preview-06-17';
        const GEMINI_API_URL = `https://generativelanguage.googleapis.com/v1beta/models/${GEMINI_MODEL}:generateContent?key=${GEMINI_API_KEY}`;

        // 语言名称映射（用于API请求）
        const LANGUAGE_NAMES = {
            'zh-cn': 'Simplified Chinese',
            'zh-tw': 'Traditional Chinese', 
            'en': 'English',
            'ms': 'Malay',
            'id': 'Indonesian',
            'ja': 'Japanese',
            'ko': 'Korean',
            'th': 'Thai',
            'vi': 'Vietnamese',
            'ru': 'Russian'
        };

        /**
         * @function performTranslations - 执行翻译
         * @param {string} text - 要翻译的文本
         * @param {Array} languages - 目标语言数组
         * @returns {Object} 翻译结果对象
         */
        async function performTranslations(text, languages) {
            const translations = {};
            
            // 限制并发数量避免API限制，每次最多处理3个请求
            const batchSize = 3;
            const batches = [];
            
            for (let i = 0; i < languages.length; i += batchSize) {
                batches.push(languages.slice(i, i + batchSize));
            }
            
            for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
                const batch = batches[batchIndex];
                
                // 更新进度
                updateTranslationProgress(`正在翻译第 ${batchIndex + 1}/${batches.length} 批次: ${batch.map(lang => LANGUAGE_NAMES[lang] || lang).join(', ')}`);
                
                const batchPromises = batch.map(async (lang) => {
                    try {
                        const translatedText = await retryTranslation(text, lang);
                        return { lang, translatedText, success: true };
                    } catch (error) {
                        console.error(`翻译到 ${lang} 失败:`, error);
                        return { 
                            lang, 
                            translatedText: `❌ 翻译失败: ${error.message}`, 
                            success: false 
                        };
                    }
                });

                // 等待当前批次完成
                const batchResults = await Promise.all(batchPromises);
                
                // 组织结果
                batchResults.forEach(({ lang, translatedText, success }) => {
                    translations[lang] = translatedText;
                    if (success) {
                        console.log(`✅ ${lang} 翻译完成`);
                    }
                });
                
                // 更新完成进度
                const completedCount = Object.keys(translations).length;
                updateTranslationProgress(`已完成 ${completedCount}/${languages.length} 种语言翻译`);
                
                // 批次之间稍作停顿，避免API限制
                if (batchIndex < batches.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
            }
            
            return translations;
        }

        /**
         * @function translateWithGemini - 使用Gemini API进行翻译
         * @param {string} text - 要翻译的文本
         * @param {string} targetLang - 目标语言代码
         * @returns {string} 翻译结果
         */
        async function translateWithGemini(text, targetLang) {
            const targetLanguageName = LANGUAGE_NAMES[targetLang] || targetLang;
            
            // 构建提示词，包含风格要求
            const styleInstructions = getStyleInstructions(selectedStyle);
            
            // 获取风格对应的emoji
            const styleEmojis = getStyleEmojis(selectedStyle);
            const emojiList = styleEmojis.join(', ');
            
            const prompt = `As a professional translator, translate the following business content into ${targetLanguageName}.

**Style Guide:**
${styleInstructions}

**Formatting Rules:**
- Use **bold** for important keywords.
- Use __underline__ for actions or deadlines.
- Add 1-2 relevant emojis from this list: ${emojiList}.

**Source Text:**
"${text}"

---
**IMPORTANT INSTRUCTIONS:**
Your response must contain ONLY the translated text.
Do not include any introductory phrases, labels, explanations, or any text other than the translation itself.
The entire response should start directly with the translated content.`;

            const requestBody = {
                contents: [{
                    parts: [{
                        text: prompt
                    }]
                }],
                generationConfig: {
                    temperature: 0.1, // 降低随机性，提高稳定性
                    topK: 1,
                    topP: 0.95,
                    maxOutputTokens: 4096, // 增加输出长度限制
                    candidateCount: 1,
                    stopSequences: [] // 不设置停止序列，避免意外截断
                },
                safetySettings: [
                    {
                        category: "HARM_CATEGORY_HARASSMENT",
                        threshold: "BLOCK_NONE"
                    },
                    {
                        category: "HARM_CATEGORY_HATE_SPEECH", 
                        threshold: "BLOCK_NONE"
                    },
                    {
                        category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                        threshold: "BLOCK_NONE"
                    },
                    {
                        category: "HARM_CATEGORY_DANGEROUS_CONTENT",
                        threshold: "BLOCK_NONE"
                    }
                ]
            };

            try {
                // 创建超时控制器
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时
                
                const response = await fetch(GEMINI_API_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody),
                    signal: controller.signal
                });

                clearTimeout(timeoutId); // 清除超时定时器

                if (!response.ok) {
                    let errorMessage = `HTTP ${response.status}`;
                    try {
                        const errorData = await response.json();
                        errorMessage = errorData.error?.message || errorMessage;
                    } catch (e) {
                        // 解析错误响应失败，使用状态码
                    }
                    throw new Error(`API请求失败: ${errorMessage}`);
                }

                const data = await response.json();
                
                // 详细检查响应结构
                if (!data.candidates || data.candidates.length === 0) {
                    throw new Error('API没有返回翻译候选结果');
                }
                
                const candidate = data.candidates[0];
                if (!candidate.content || !candidate.content.parts || candidate.content.parts.length === 0) {
                    throw new Error('API返回的内容结构不完整');
                }
                
                const translatedText = candidate.content.parts[0].text;
                if (!translatedText || translatedText.trim() === '') {
                    throw new Error('API返回了空的翻译结果');
                }
                
                return translatedText.trim();
                
            } catch (error) {
                if (error.name === 'AbortError') {
                    console.error(`Gemini API调用超时 (${targetLang})`);
                    throw new Error('翻译请求超时，请稍后重试');
                } else {
                    console.error(`Gemini API调用失败 (${targetLang}):`, error);
                    throw error;
                }
            }
        }

        /**
         * @function getStyleInstructions - 获取风格指导
         * @param {string} style - 选择的风格
         * @returns {string} 风格指导文本
         */
        function getStyleInstructions(style) {
            const styleMap = {
                'formal': 'Use formal, professional language with respectful expressions. Add courtesy phrases where appropriate. Maintain official tone suitable for policies and announcements.',
                'concise': 'Use concise, clear language. Remove unnecessary words while maintaining clarity and professionalism. Focus on essential information.',
                'detailed': 'Use detailed, comprehensive language. Add explanatory phrases and additional context where helpful. Provide thorough instructions and explanations.',
                'friendly': 'Use warm, approachable language that creates connection. Add friendly expressions, use inclusive tone, and make the content feel welcoming and personable.',
                'courteous': 'Use highly polite and elegant language. Employ respectful terms, gracious expressions, and sophisticated courtesy that reflects refinement and respect.',
                'marketing': 'Use persuasive, engaging language that attracts attention. Incorporate compelling terms, emphasize benefits, and create excitement about offerings or opportunities.',
                'caring': 'Use compassionate, nurturing language that shows genuine concern. Express empathy, understanding, and supportive sentiment to create emotional connection.',
                'gentle': 'Use soothing, calming language that reduces tension. Choose mild terms, avoid harsh expressions, and create a reassuring, comfortable tone.',
                'urgent': 'Use immediate, action-oriented language that conveys urgency. Emphasize time sensitivity, importance, and the need for prompt action.',
                'strong': 'Use firm, decisive language with confident assertions. Employ strong terms, clear directives, and authoritative tone that conveys certainty and determination.'
            };
            
            return styleMap[style] || styleMap['concise'];
        }

        /**
         * @function showLoadingState - 显示加载状态
         * @description 在翻译过程中显示加载动画
         */
        function showLoadingState() {
            const resultsSection = document.getElementById('resultsSection');
            resultsSection.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    正在使用Gemini AI进行专业翻译，请稍候...
                    <br><small style="color: #888; margin-top: 10px; display: block;">
                        ✨ 已优化翻译完整性 | 🔄 自动重试机制 | ⏱️ 通常需要10-30秒
                    </small>
                    <div id="translationProgress" style="margin-top: 15px; font-size: 0.9rem; color: #666;">
                        准备开始翻译...
                    </div>
                </div>
            `;
        }

        /**
         * @function updateTranslationProgress - 更新翻译进度
         * @param {string} message - 进度消息
         */
        function updateTranslationProgress(message) {
            const progressElement = document.getElementById('translationProgress');
            if (progressElement) {
                progressElement.innerHTML = message;
            }
        }

        /**
         * @function displayResults - 显示翻译结果
         * @param {Object} translations - 翻译结果对象
         * @param {boolean} saveHistory - 是否保存到历史记录
         * @description 在页面上显示所有翻译结果
         */
        function displayResults(translations, saveHistory = true) {
            const resultsSection = document.getElementById('resultsSection');
            
            const resultsGrid = document.createElement('div');
            resultsGrid.className = 'results-grid';
            
            Object.entries(translations).forEach(([langCode, translatedText]) => {
                const resultCard = createResultCard(langCode, translatedText);
                resultsGrid.appendChild(resultCard);
            });
            
            resultsSection.innerHTML = '';
            resultsSection.appendChild(resultsGrid);
            
            // 保存到历史记录（仅在新翻译时保存）
            if (saveHistory) {
                const originalText = document.getElementById('originalText').value.trim();
                saveTranslationHistory(originalText, selectedStyle, translations);
            }
        }

        /**
         * @function createResultCard - 创建结果卡片
         * @param {string} langCode - 语言代码
         * @param {string} translatedText - 翻译后的文本
         * @returns {HTMLElement} 结果卡片DOM元素
         */
        function createResultCard(langCode, translatedText) {
            const card = document.createElement('div');
            card.className = 'result-card minimized'; // 默认最小化
            
            const languageConfig = LANGUAGE_CONFIG[langCode];
            const languageName = languageConfig ? languageConfig.name : langCode;
            
            // 使用新的格式化文本解析
            const formattedText = parseFormattedText(translatedText, selectedStyle);
            
            // 为复制功能准备纯文本版本（完全清理所有符号）
            const plainText = cleanTextForCopy(translatedText);
            
            card.innerHTML = `
                <div class="result-header">
                    <h3 class="language-title">${languageName}<span class="expand-icon">▼</span></h3>
                    <button class="copy-btn">复制</button>
                </div>
                <div class="result-content">
                    <div class="result-text">${formattedText}</div>
                </div>
            `;
            
            // 添加事件监听器
            const titleElement = card.querySelector('.language-title');
            const copyButton = card.querySelector('.copy-btn');
            
            titleElement.addEventListener('click', () => {
                card.classList.toggle('minimized');
            });
            
            copyButton.addEventListener('click', (e) => {
                e.stopPropagation(); // 防止事件冒泡
                copyToClipboard(copyButton, plainText);
            });
            
            return card;
        }



        /**
         * @function copyToClipboard - 复制到剪贴板
         * @param {HTMLElement} button - 点击的按钮元素
         * @param {string} text - 要复制的文本
         * @description 将文本复制到剪贴板并更新按钮状态
         */
        async function copyToClipboard(button, text) {
            console.log('copyToClipboard called with text:', text); // 调试信息
            
            try {
                // 检查剪贴板API是否可用
                if (navigator.clipboard && window.isSecureContext) {
                    await navigator.clipboard.writeText(text);
                    console.log('使用Clipboard API复制成功'); // 调试信息
                } else {
                    throw new Error('Clipboard API不可用，使用降级方案');
                }
                
                // 更新按钮状态
                const originalText = button.textContent;
                button.textContent = '已复制';
                button.classList.add('copied');
                
                // 2秒后恢复原状态
                setTimeout(() => {
                    button.textContent = originalText;
                    button.classList.remove('copied');
                }, 2000);
                
            } catch (err) {
                console.error('复制失败，尝试降级方案:', err);
                
                try {
                    // 降级方案：使用旧的复制方法
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.position = 'fixed';
                    textArea.style.opacity = '0';
                    document.body.appendChild(textArea);
                    textArea.select();
                    textArea.setSelectionRange(0, 99999); // 为移动设备
                    
                    const successful = document.execCommand('copy');
                    document.body.removeChild(textArea);
                    
                    if (successful) {
                        console.log('使用execCommand复制成功'); // 调试信息
                        button.textContent = '已复制';
                        button.classList.add('copied');
                        setTimeout(() => {
                            button.textContent = '复制';
                            button.classList.remove('copied');
                        }, 2000);
                    } else {
                        throw new Error('execCommand复制也失败了');
                    }
                } catch (fallbackErr) {
                    console.error('所有复制方法都失败了:', fallbackErr);
                    button.textContent = '复制失败';
                    button.style.background = '#dc3545';
                    setTimeout(() => {
                        button.textContent = '复制';
                        button.style.background = '';
                    }, 2000);
                }
            }
        }

        /**
         * @function escapeHtml - 转义HTML字符
         * @param {string} text - 要转义的文本
         * @returns {string} 转义后的文本
         */
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 不同风格的Emoji配置
        const STYLE_EMOJIS = {
            formal: ['✓', '⚠️', '📋', '📊', '⭐'],
            concise: ['→', '•', '✓', '📌', '💡'],
            detailed: ['📋', '📝', '🔍', '📊', '📋', '✏️'],
            friendly: ['😊', '🤝', '💝', '✨', '🌟', '💫', '🎈'],
            courteous: ['🙏', '💐', '✨', '👑', '🌹', '💎', '🎭'],
            marketing: ['🔥', '⭐', '💎', '🎉', '🚀', '💥', '🌟', '✨'],
            caring: ['❤️', '🤗', '💐', '🌟', '💝', '🌸', '🌺'],
            gentle: ['😌', '🕊️', '🌸', '💙', '🌙', '☁️', '🌿'],
            urgent: ['⚠️', '🚨', '⏰', '❗', '🔴', '⚡', '📢'],
            strong: ['💪', '✊', '🎯', '⚡', '🔥', '💥', '🛡️']
        };

        /**
         * @function cleanTextForCopy - 清理文本用于复制（移除所有格式符号）
         * @param {string} text - 包含格式标记的文本
         * @returns {string} 完全清理后的纯文本
         */
        function cleanTextForCopy(text) {
            if (!text) return '';
            
            return text
                // 移除Markdown标题符号 # ## ### 等
                .replace(/^#{1,6}\s+/gm, '')
                // 移除加粗格式 **text**
                .replace(/\*\*(.*?)\*\*/g, '$1')
                // 移除下划线格式 __text__
                .replace(/__(.*?)__/g, '$1')
                // 移除高亮格式 ==text==
                .replace(/==(.*?)==/g, '$1')
                // 移除所有星号
                .replace(/\*/g, '')
                // 移除所有下划线（单独的）
                .replace(/(?<!\w)_(?!\w)/g, '')
                // 移除其他常见的Markdown符号
                .replace(/^>\s+/gm, '') // 移除引用符号 >
                .replace(/^-{3,}$/gm, '') // 移除分隔线 ---
                .replace(/^={3,}$/gm, '') // 移除分隔线 ===
                .replace(/^\s*[\*\-\+]\s+/gm, '') // 移除列表符号
                .replace(/^\s*\d+\.\s+/gm, '') // 移除有序列表序号
                .replace(/^#{1,6}\s*/gm, '') // 再次确保移除所有井号
                // 移除反引号
                .replace(/`{1,3}/g, '')
                // 移除方括号链接格式 [text](url)
                .replace(/\[([^\]]+)\]\([^\)]+\)/g, '$1')
                // 清理多余的空行
                .replace(/\n{3,}/g, '\n\n')
                // 清理开头和结尾的空白
                .trim();
        }

        /**
         * @function parseFormattedText - 解析格式化文本
         * @param {string} text - 包含格式标记的文本
         * @param {string} style - 当前选择的风格
         * @returns {string} 转换为HTML的格式化文本
         */
        function parseFormattedText(text, style) {
            if (!text) return '';
            
            // 首先清理不必要的Markdown符号和格式标记
            let cleanedText = text
                // 移除Markdown标题符号 # ## ### 等
                .replace(/^#{1,6}\s+/gm, '')
                // 移除多余的星号（但保留我们的格式标记）
                .replace(/\*{3,}/g, '') // 移除3个或更多连续星号
                // 移除多余的下划线
                .replace(/_{3,}/g, '') // 移除3个或更多连续下划线
                // 移除其他常见的Markdown符号
                .replace(/^>\s+/gm, '') // 移除引用符号 >
                .replace(/^-{3,}$/gm, '') // 移除分隔线 ---
                .replace(/^={3,}$/gm, '') // 移除分隔线 ===
                .replace(/^\s*[\*\-\+]\s+/gm, '') // 移除列表符号但保留内容
                .replace(/^\s*\d+\.\s+/gm, '') // 移除有序列表序号
                // 清理多余的空行
                .replace(/\n{3,}/g, '\n\n')
                // 清理开头和结尾的空白
                .trim();
            
            // 转义HTML特殊字符，但保留我们的格式标记
            let formattedText = cleanedText
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&#39;');
            
            // 处理加粗文本 **text**
            formattedText = formattedText.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
            
            // 处理下划线文本 __text__
            formattedText = formattedText.replace(/__(.*?)__/g, '<span class="underline">$1</span>');
            
            // 处理高亮文本 ==text==
            formattedText = formattedText.replace(/==(.*?)==/g, '<span class="highlight">$1</span>');
            
            // 清理残留的单个星号或下划线（避免显示格式错误）
            formattedText = formattedText
                .replace(/(?<!<[^>]*)\*(?![^<]*>)/g, '') // 移除不在HTML标签内的单个星号
                .replace(/(?<!<[^>]*_)_(?![^<]*>)/g, '') // 移除不在HTML标签内的单个下划线
                .replace(/(?<!<[^>]*)#(?![^<]*>)/g, ''); // 移除不在HTML标签内的井号
            
            // 根据风格添加特殊高亮
            const styleClass = getStyleHighlightClass(style);
            if (styleClass) {
                // 查找并高亮关键词
                const keywords = getStyleKeywords(style);
                keywords.forEach(keyword => {
                    const regex = new RegExp(`\\b(${keyword})\\b`, 'gi');
                    formattedText = formattedText.replace(regex, `<span class="${styleClass}">$1</span>`);
                });
            }
            
            // 处理换行
            formattedText = formattedText.replace(/\n/g, '<br>');
            
            return formattedText;
        }

        /**
         * @function getStyleHighlightClass - 获取风格对应的高亮样式类
         * @param {string} style - 风格名称
         * @returns {string} CSS类名
         */
        function getStyleHighlightClass(style) {
            const styleClasses = {
                urgent: 'urgent-highlight',
                friendly: 'friendly-highlight',
                marketing: 'marketing-highlight'
            };
            return styleClasses[style] || '';
        }

        /**
         * @function getStyleKeywords - 获取风格对应的关键词
         * @param {string} style - 风格名称
         * @returns {Array} 关键词数组
         */
        function getStyleKeywords(style) {
            const keywords = {
                urgent: ['立即', '紧急', '马上', '重要', 'urgent', 'immediate', 'important'],
                friendly: ['欢迎', '感谢', '很高兴', 'welcome', 'thank', 'pleased'],
                marketing: ['优惠', '特价', '限时', '专属', 'special', 'exclusive', 'limited'],
                caring: ['关怀', '照顾', '健康', '安全', 'care', 'health', 'safety'],
                strong: ['必须', '坚持', '确保', 'must', 'ensure', 'guarantee']
            };
            return keywords[style] || [];
        }

        /**
         * @function getStyleEmojis - 获取风格对应的emoji
         * @param {string} style - 风格名称
         * @returns {Array} emoji数组
         */
        function getStyleEmojis(style) {
            return STYLE_EMOJIS[style] || STYLE_EMOJIS.concise;
        }

        /**
         * @function isTranslationComplete - 检查翻译是否完整
         * @param {string} originalText - 原始文本
         * @param {string} translatedText - 翻译后的文本
         * @returns {boolean} 翻译是否完整
         */
        function isTranslationComplete(originalText, translatedText) {
            if (!translatedText || translatedText.trim() === '') {
                return false;
            }
            
            // 检查是否以省略号或截断标记结尾
            if (translatedText.endsWith('...') || translatedText.endsWith('…')) {
                return false;
            }
            
            // 检查长度是否合理（翻译不应该比原文短太多）
            const originalLength = originalText.length;
            const translatedLength = translatedText.length;
            
            // 如果翻译长度小于原文的30%，可能不完整
            if (translatedLength < originalLength * 0.3) {
                return false;
            }
            
            // 检查是否包含常见的截断标记
            const truncationMarkers = ['[截断]', '[不完整]', '...继续', 'continued...', '[cut off]'];
            for (const marker of truncationMarkers) {
                if (translatedText.includes(marker)) {
                    return false;
                }
            }
            
            return true;
        }

        /**
         * @function retryTranslation - 重试翻译
         * @param {string} text - 要翻译的文本
         * @param {string} targetLang - 目标语言
         * @param {number} retryCount - 重试次数
         * @returns {string} 翻译结果
         */
        async function retryTranslation(text, targetLang, retryCount = 0) {
            const maxRetries = 3;
            
            try {
                const result = await translateWithGemini(text, targetLang);
                
                if (isTranslationComplete(text, result)) {
                    return result;
                } else if (retryCount < maxRetries) {
                    console.log(`翻译到${targetLang}不完整，重试第${retryCount + 1}次`);
                    await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒后重试
                    return retryTranslation(text, targetLang, retryCount + 1);
                } else {
                    console.warn(`翻译到${targetLang}经过${maxRetries}次重试仍不完整`);
                    return result + ' [⚠️ 翻译可能不完整]';
                }
            } catch (error) {
                if (retryCount < maxRetries) {
                    console.log(`翻译到${targetLang}失败，重试第${retryCount + 1}次:`, error);
                    await new Promise(resolve => setTimeout(resolve, 2000)); // 等待2秒后重试
                    return retryTranslation(text, targetLang, retryCount + 1);
                } else {
                    throw error;
                }
            }
        }

        /**
         * @function setupHistoryControls - 设置历史记录控制按钮
         * @description 为历史记录相关按钮添加事件监听器
         */
        function setupHistoryControls() {
            const toggleBtn = document.getElementById('toggleHistoryBtn');
            const clearBtn = document.getElementById('clearHistoryBtn');
            
            toggleBtn.addEventListener('click', toggleHistoryVisibility);
            clearBtn.addEventListener('click', clearTranslationHistory);
        }

        /**
         * @function saveTranslationHistory - 保存翻译历史记录
         * @param {string} originalText - 原始文本
         * @param {string} style - 选择的风格
         * @param {Object} translations - 翻译结果
         */
        function saveTranslationHistory(originalText, style, translations) {
            try {
                const history = getTranslationHistory();
                
                const newEntry = {
                    id: Date.now().toString(),
                    timestamp: new Date().toISOString(),
                    originalText: originalText,
                    style: style,
                    translations: translations,
                    languages: Object.keys(translations)
                };
                
                history.unshift(newEntry); // 添加到开头
                
                // 限制历史记录数量为50条
                if (history.length > 50) {
                    history.splice(50);
                }
                
                localStorage.setItem(HISTORY_STORAGE_KEY, JSON.stringify(history));
                loadTranslationHistory(); // 刷新显示
                
            } catch (error) {
                console.error('保存历史记录失败:', error);
            }
        }

        /**
         * @function getTranslationHistory - 获取翻译历史记录
         * @returns {Array} 历史记录数组
         */
        function getTranslationHistory() {
            try {
                const history = localStorage.getItem(HISTORY_STORAGE_KEY);
                return history ? JSON.parse(history) : [];
            } catch (error) {
                console.error('读取历史记录失败:', error);
                return [];
            }
        }

        /**
         * @function loadTranslationHistory - 加载并显示翻译历史记录
         */
        function loadTranslationHistory() {
            const history = getTranslationHistory();
            const historyList = document.getElementById('historyList');
            
            if (history.length === 0) {
                historyList.innerHTML = `
                    <div style="text-align: center; padding: 20px; color: #666;">
                        暂无翻译历史记录
                    </div>
                `;
                return;
            }
            
            historyList.innerHTML = history.map(entry => createHistoryItem(entry)).join('');
        }

        /**
         * @function createHistoryItem - 创建历史记录项目
         * @param {Object} entry - 历史记录条目
         * @returns {string} HTML字符串
         */
        function createHistoryItem(entry) {
            const date = new Date(entry.timestamp);
            const formattedDate = date.toLocaleString('zh-CN');
            const preview = entry.originalText.length > 100 
                ? entry.originalText.substring(0, 100) + '...' 
                : entry.originalText;
            
            const languageNames = entry.languages.map(lang => 
                LANGUAGE_CONFIG[lang]?.name || lang
            ).join(', ');
            
            // 为历史记录预览添加格式化支持
            const formattedPreview = parseFormattedText(preview, entry.style);
            
            return `
                <div class="history-item" data-id="${entry.id}">
                    <div class="history-item-header">
                        <div class="history-meta">
                            <div><strong>时间:</strong> ${formattedDate}</div>
                            <div><strong>风格:</strong> ${STYLE_TEMPLATES[entry.style]?.name || entry.style}</div>
                            <div><strong>语言:</strong> ${languageNames}</div>
                        </div>
                        <div class="history-actions">
                            <button class="history-action-btn restore" onclick="restoreFromHistory('${entry.id}')">
                                恢复
                            </button>
                            <button class="history-action-btn delete" onclick="deleteHistoryItem('${entry.id}')">
                                删除
                            </button>
                        </div>
                    </div>
                    <div class="history-preview">${formattedPreview}</div>
                </div>
            `;
        }

        /**
         * @function toggleHistoryVisibility - 切换历史记录可见性
         */
        function toggleHistoryVisibility() {
            const historyList = document.getElementById('historyList');
            const toggleBtn = document.getElementById('toggleHistoryBtn');
            
            isHistoryVisible = !isHistoryVisible;
            
            if (isHistoryVisible) {
                historyList.classList.remove('hidden');
                toggleBtn.textContent = '隐藏历史记录';
                loadTranslationHistory(); // 刷新历史记录
            } else {
                historyList.classList.add('hidden');
                toggleBtn.textContent = '显示历史记录';
            }
        }

        /**
         * @function clearTranslationHistory - 清空翻译历史记录
         */
        function clearTranslationHistory() {
            if (confirm('确定要清空所有翻译历史记录吗？此操作不可撤销。')) {
                localStorage.removeItem(HISTORY_STORAGE_KEY);
                loadTranslationHistory();
                alert('历史记录已清空');
            }
        }

        /**
         * @function restoreFromHistory - 从历史记录恢复翻译
         * @param {string} entryId - 历史记录条目ID
         */
        function restoreFromHistory(entryId) {
            const history = getTranslationHistory();
            const entry = history.find(item => item.id === entryId);
            
            if (!entry) {
                alert('历史记录不存在');
                return;
            }
            
            // 恢复原始文本
            document.getElementById('originalText').value = entry.originalText;
            
            // 恢复风格选择
            document.querySelectorAll('.style-option').forEach(option => {
                option.classList.toggle('selected', option.dataset.style === entry.style);
            });
            selectedStyle = entry.style;
            
            // 恢复语言选择
            document.querySelectorAll('.language-checkbox input[type="checkbox"]').forEach(checkbox => {
                checkbox.checked = entry.languages.includes(checkbox.value);
            });
            
            // 显示翻译结果（不保存历史记录）
            displayResults(entry.translations, false);
            
            // 更新按钮状态
            updateTranslateButtonState();
            
            alert('已恢复历史翻译记录');
        }

        /**
         * @function deleteHistoryItem - 删除单个历史记录项目
         * @param {string} entryId - 历史记录条目ID
         */
        function deleteHistoryItem(entryId) {
            if (confirm('确定要删除这条历史记录吗？')) {
                const history = getTranslationHistory();
                const filteredHistory = history.filter(item => item.id !== entryId);
                localStorage.setItem(HISTORY_STORAGE_KEY, JSON.stringify(filteredHistory));
                loadTranslationHistory();
            }
        }

        // 监听输入变化以更新按钮状态
        document.addEventListener('DOMContentLoaded', () => {
            initializeApp();
            
            const originalTextArea = document.getElementById('originalText');
            originalTextArea.addEventListener('input', updateTranslateButtonState);
        });
    </script>
</body>
</html> 