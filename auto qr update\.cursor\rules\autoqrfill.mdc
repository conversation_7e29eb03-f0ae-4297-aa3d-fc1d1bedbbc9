---
description: 
globs: 
alwaysApply: true
---
---
description: 
globs: 
alwaysApply: true
---
# AutoQRFill 工作规则 - GoMyHire QR码翻译系统

**规则版本**: v7.1 (精简版)
**核心思想**: 严格遵循流程，优先保证状态一致性与操作验证。

---

## 🚨 **强制黄金操作守则 (MANDATORY GOLDEN RULES)**

**以下规则为最高优先级，必须无条件遵守，禁止任何形式的变通、推断或跳过。**

### **1. 环境与状态一致性 (Environment & State)**
- **必须重置环境**: 在处理任何新 `qrcode ID` 前，**必须连续按 `Escape` 键三次**，关闭所有残留浮窗。
- **必须禁用分页**: 进入任何列表后，**必须立即将显示条数设为100**，防止数据获取不完整。

### **2. 严格遵循流程 (Process)**
- **禁止跳过或推断**: **永远不要**基于经验预估或跳过任何步骤。
- **先验证后操作**: 在浮窗内执行任何操作前，**必须先验证正确的ID浮窗已成功打开**，这是所有后续操作的前提。
- **验证执行结果**: 每次关键操作（如提交表单）后，**必须**验证其结果（如数据更新、UI变化）。

### **3. UI交互与数据操作 (Interaction & Data)**
- **UI交互失败的标准方案**: 如果 `chrome_click_element` 失败（特别是浮窗内），**必须切换到 `chrome_inject_script` (ISOLATED) 直接调用其JS函数** (如 `qrCodeSubTranslate('sub_id')`)。这是标准备选方案，不是跳过步骤。
- **处理隐藏元素**: 必须考虑并处理滚动条内或当前不可见的元素。

### **4. 内容的唯一真实来源 (Single Source of Truth)**
- **指定翻译源**: 修改或添加翻译时，**永远且唯一**使用 `C:\Users\<USER>\Downloads\翻译\auto qr update\business-translation-tool.md` 作为内容来源。
- **处理内容缺失**: 若上述文件无内容，**严禁**自行编写或复制。**必须**创建空白模板（Description和Remark留空）并记录。

---

## 🚀 **标准化作业流程 (SOP)**

1.  **定位项目**: 找到目标 `qrcode ID`，点击 `Edit` 进入主编辑窗。 **(遵守黄金守则 #1, #2)**
2.  **提取子项目**: 获取所有子项目 (`subqr`) 的列表。
3.  **循环审查**: 对每个子项目执行以下操作：
    a. **打开翻译窗口**: 使用 `qrCodeSubTranslate('sub_id')` 打开翻译管理窗口。 **(遵守黄金守则 #3)**
    b. **验证Sub ID**: 确认浮窗内的 `sub_id` 与目标一致。
    c. **记录状态**: 获取并记录翻译数量 (`X/10`) 和已有语言。
    d. **关闭窗口**: 按 `Escape` 键。
4.  **翻译补充/修正**:
    a. **打开添加/编辑窗口**: 点击 `Add Translate` 或 `Edit` 按钮。
    b. **执行操作**: 使用下文的 **"JS标准表单处理脚本"** 配合 **"黄金守则 #4"** 的内容源进行操作。
    c. **验证结果**: 确认翻译数量已更新。
5.  **完成与重置**: 完成所有操作后，按 `Escape` 键三次，彻底重置环境。

---

## 🔧 **核心技术与选择器规范**

### **工具使用优先级**
- **数据提取**: **永远**使用 `chrome_get_web_content`。
- **UI点击**:
    1.  `chrome_click_element` (首选)。
    2.  `chrome_inject_script` (标准备选，当首选失败时)。
- **表单填写/提交**: **永远**使用下方的 **"JS标准表单处理脚本"**。

### **CSS选择器最佳实践**
- **精确定位**: 永远使用最精确的选择器，避免内容截断。
    - `... tbody` 用于表格数据。
    - `..._info` 用于表格统计信息。
- **浮窗内元素**: **必须**使用浮窗ID作为前缀。
    - **正确**: `#qrCodeSubTranslateModal #some_element`
    - **严禁**: `#some_element` (无前缀)
- **关键按钮定位**:
    - **子项目Translate**: **只使用`button`元素**，`a`标签不稳定。
      `#qrCodeMainModal button[onclick="qrCodeSubTranslate('sub_id')"]`
    - **提交按钮**:
      `#qrCodeSubTranslateEditModal button[onclick*="submit_qrCodeSubTranslate_form"]`

---

## 🚀 **JS标准表单处理脚本**

**此脚本是添加或编辑翻译的唯一标准方法，能处理浮窗显示、Cloudflare保护和表单提交问题。**

```javascript
// 标准化翻译添加/编辑流程
chrome_inject_script({
    type: "MAIN",
    jsScript: `
        console.log('🚀 开始标准表单处理流程...');

        // 步骤1: 强制显示浮窗
        const modal = document.getElementById('qrCodeSubTranslateEditModal');
        if (modal) {
            modal.classList.add('show');
            modal.style.display = 'block';
            modal.setAttribute('aria-hidden', 'false');

            if (!document.querySelector('.modal-backdrop')) {
                const backdrop = document.createElement('div');
                backdrop.className = 'modal-backdrop fade show';
                document.body.appendChild(backdrop);
            }
            console.log('✅ 浮窗显示修复完成');
        }

        // 等待浮窗完全显示
        setTimeout(() => {
            // 步骤2: 填写表单
            const langSelect = document.getElementById('qrCodeSubTranslate_lang');
            const descTextarea = document.getElementById('qrCodeSubTranslate_description');
            const remarkTextarea = document.getElementById('qrCodeSubTranslate_remark');

            if (langSelect && descTextarea && remarkTextarea) {
                // 将 '语言代码', '翻译描述内容', '翻译备注内容' 替换为实际数据
                langSelect.value = '语言代码';
                langSelect.dispatchEvent(new Event('change', { bubbles: true }));

                descTextarea.value = '翻译描述内容';
                descTextarea.dispatchEvent(new Event('input', { bubbles: true }));

                remarkTextarea.value = '翻译备注内容';
                remarkTextarea.dispatchEvent(new Event('input', { bubbles: true }));

                console.log('✅ 表单填写完成');

                // 步骤3: 设置Cloudflare标志并提交
                if (!window.__cfRLUnblockHandlers) {
                    window.__cfRLUnblockHandlers = true;
                }

                setTimeout(() => {
                    if (typeof submit_qrCodeSubTranslate_form === 'function') {
                        submit_qrCodeSubTranslate_form();
                        console.log('✅ 已提交翻译');
                    } else {
                        console.error('❌ 提交函数不存在');
                    }
                }, 1000);

            } else {
                console.error('❌ 表单元素未找到');
            }
        }, 1000);

        return true;
    `
})
```
**在使用此脚本时，请确保将`'语言代码'`、`'翻译描述内容'`和`'翻译备注内容'`替换为从 `business-translation-tool.md` 中获取的实际数据。**

---

## 📋 **完整翻译操作流程详解 (Complete Translation Workflow)**

### **阶段1: 准备与分析 (Preparation & Analysis)**

#### **1.1 工具链初始化**
- **必须使用 `sequential-thinking` 工具**进行任务分析和步骤规划
- **必须使用 `mcp-taskmanager`** 制定详细任务清单
- **必须使用 `streamable-mcp-server`** 执行所有浏览器操作

#### **1.2 翻译内容源确认**
- **唯一数据源**: `C:\Users\<USER>\Downloads\翻译\auto qr update\business-translation-tool.md`
- **查找策略**: 使用关键词搜索（如"Eagle Feeding"、"Fireflies"、"blue tear"）
- **内容匹配**: 根据QR码服务类型匹配对应的翻译模板
- **标准格式**: 确保Description和Remark内容完整且符合业务标准

### **阶段2: 浏览器操作执行 (Browser Operations)**

#### **2.1 表单字段填写顺序**
**严格按照以下顺序执行，不可跳过或调换：**

1. **语言选择** (`qrCodeSubTranslate_lang`)
   - 使用 `chrome_fill_or_select` 工具
   - 目标值: `zh-CN` (简体中文)
   - 验证: 确认下拉框值已正确设置

2. **Description字段填写** (`qrCodeSubTranslate_description`)
   - 使用 `chrome_fill_or_select` 工具
   - 内容来源: `business-translation-tool.md` 中对应服务的Description
   - 示例: "瓜拉雪兰莪综合船游票务服务，包含老鹰喂食、萤火虫观赏和蓝眼泪体验"

3. **Remark字段填写** (`qrCodeSubTranslate_remark`)
   - 使用 `chrome_fill_or_select` 工具
   - 内容来源: `business-translation-tool.md` 中对应服务的完整Remark
   - 包含: 下单时间、票务内容、价格政策、注意事项、拍摄规定、防护措施、行程时长、退款政策、体验顺序等

#### **2.2 表单提交处理**
**当Submit按钮不可见时的标准处理流程：**

1. **首次尝试**: 使用 `chrome_click_element` 点击 `button.btn.btn-primary`
2. **备选方案**: 如果按钮不可见，使用 `chrome_inject_script` 直接调用提交函数：
   ```javascript
   if (typeof submit_qrCodeSubTranslate_form === 'function') {
       submit_qrCodeSubTranslate_form();
       console.log('Form submitted successfully');
   } else {
       console.log('Submit function not found');
   }
   ```
3. **结果验证**: 使用 `chrome_get_web_content` 确认页面已返回到QR Code列表

### **阶段3: 任务管理与验证 (Task Management & Verification)**

#### **3.1 任务清单管理**
**使用 `mcp-taskmanager` 的标准流程：**

1. **任务规划** (`request_planning`)
   - 创建包含所有操作步骤的任务清单
   - 每个任务必须有明确的标题和描述

2. **任务执行循环**
   - 使用 `get_next_task` 获取下一个待执行任务
   - 执行具体操作
   - 使用 `mark_task_done` 标记任务完成
   - **关键**: 必须等待 `approve_task_completion` 批准后才能继续

3. **最终确认**
   - 所有任务完成后使用 `approve_request_completion` 最终确认

#### **3.2 操作验证要点**

**每个步骤完成后必须验证：**
- ✅ 表单字段值是否正确填入
- ✅ 页面状态是否符合预期
- ✅ 是否有错误提示或异常
- ✅ 提交后是否成功返回列表页面

### **阶段4: 常见问题与解决方案 (Troubleshooting)**

#### **4.1 浏览器调试问题**
- **控制台被占用**: 如遇到"Debugger is already attached"错误，直接使用 `chrome_get_web_content` 验证页面状态
- **元素不可见**: 优先使用 `chrome_inject_script` 直接操作DOM或调用JS函数
- **表单提交失败**: 检查网络请求，确认Cloudflare保护是否影响提交

#### **4.2 数据匹配问题**
- **翻译内容缺失**: 如果 `business-translation-tool.md` 中找不到对应内容，必须报告并停止操作
- **服务类型不匹配**: 仔细核对QR码ID与翻译模板的对应关系
- **多语言冲突**: 确保选择正确的语言代码，避免覆盖已有翻译

#### **4.3 工具链协作问题**
- **MCP服务器连接**: 确保所有MCP服务器正常响应
- **任务状态同步**: 严格按照任务管理流程，避免跳过批准步骤
- **浏览器状态**: 定期检查浏览器标签页状态，确保操作在正确页面执行

---

## 🎯 **质量检查清单 (Quality Checklist)**

### **操作前检查**
- [ ] 已使用 `sequential-thinking` 分析任务
- [ ] 已制定完整的任务清单
- [ ] 已确认翻译内容来源和匹配度
- [ ] 浏览器环境已准备就绪

### **操作中检查**
- [ ] 每个表单字段都已正确填写
- [ ] 每个操作步骤都有相应验证
- [ ] 任务状态及时更新和批准
- [ ] 错误处理机制正常工作

### **操作后检查**
- [ ] 翻译内容已成功提交
- [ ] 页面状态恢复正常
- [ ] 所有任务已完成并批准
- [ ] 操作日志完整记录

---

## 📝 **操作记录模板 (Operation Log Template)**

```markdown
## 翻译操作记录 - [QR码ID] - [日期]

### 基本信息
- QR码ID: [ID]
- 服务类型: [服务名称]
- 目标语言: zh-CN
- 操作时间: [时间戳]

### 翻译内容
- Description: [具体内容]
- Remark: [具体内容]
- 内容来源: business-translation-tool.md - [具体章节]

### 操作步骤
1. [步骤1] - ✅/❌
2. [步骤2] - ✅/❌
3. [步骤3] - ✅/❌

### 遇到的问题
- [问题描述]
- [解决方案]

### 验证结果
- 表单提交: ✅/❌
- 页面跳转: ✅/❌
- 数据保存: ✅/❌
```
