# 🌐 QR Code翻译补充标准化工作流程

**版本**: v2.1
**创建时间**: 2025-01-27
**最后更新**: 2025-01-27
**适用系统**: GoMyHire QR码管理系统
**基于经验**: KTMB项目翻译补充实践 + 完全自动化脚本验证 + 逐一验证质量标准

---

## 📋 **工作流程概述**

本文档提供了一套标准化的QR Code项目翻译补充工作流程，适用于任何需要添加多语言翻译的QR Code项目。

### **标准10语言列表**
1. **English** (英语)
2. **简体中文** (Simplified Chinese)  
3. **繁體中文** (Traditional Chinese)
4. **Bahasa Melayu** (马来语)
5. **Bahasa Indonesia** (印尼语)
6. **日本語** (日语)
7. **한국어** (韩语)
8. **ภาษาไทย** (泰语)
9. **Tiếng Việt** (越南语)
10. **Русский** (俄语)

---

## 🔍 **阶段1: 项目分析与评估**

### **1.1 项目基础信息收集**
- [ ] 获取QR Code项目ID和名称
- [ ] 记录项目状态（启用/禁用）
- [ ] 统计子项目总数
- [ ] 识别服务类型分布

### **1.2 翻译状态评估**
```bash
# 系统操作步骤
1. 导航到: https://staging.gomyhire.com.my/s/qrCode
2. 点击目标项目的"Edit"按钮
3. ⚡ 设置子项目显示条数为最大值 (避免分页问题)
   - 点击显示条数选择器: select[name="qrCodeSubDataTable_length"]
   - 选择"100"选项 (最大显示条数)
   - 确认显示"Showing 1 to X of X entries"完整数据
4. 获取完整子项目列表
5. 逐一检查每个子项目的翻译状态
```

### **1.3 翻译缺口分析**
- [ ] 统计完整翻译服务数量 (10/10)
- [ ] 统计部分翻译服务数量 (1-9/10)
- [ ] 统计无翻译服务数量 (0/10)
- [ ] 计算总体翻译完整度百分比

### **1.4 优先级评估**
**高优先级服务特征:**
- 🎯 机场接送服务 (包含Airport、KLIA、Kuala Lumpur关键词)
- 🎯 云顶高原服务 (包含Genting关键词)
- 🎯 热门旅游服务 (Melaka、Kuala Selangor等)

---

## 📚 **阶段2: 翻译数据准备**

### **2.1 翻译内容来源**
1. **现有翻译库**: 检查`business-translation-tool.md`文件
2. **参考模板**: 使用同类服务的翻译作为模板
3. **新建翻译**: 为新服务类型创建翻译内容

### **2.2 翻译内容结构**
每个翻译条目包含两部分：
- **Description**: 服务描述 (50-100字)
- **Remark**: 注意事项和详细说明 (100-200字)

### **2.3 翻译质量标准**
- ✅ **准确性**: 内容与原文意思一致
- ✅ **完整性**: 包含所有关键信息
- ✅ **一致性**: 术语使用统一
- ✅ **本地化**: 符合目标语言表达习惯

---

## ⚙️ **阶段3: 系统操作执行**

### **3.1 单个翻译添加流程**
```bash
# 标准操作步骤
1. 点击子项目的"Translate"按钮
2. 点击"Add Translate"按钮
3. 选择目标语言 (Language下拉框)
4. 填入Description内容
5. 填入Remark内容
6. 点击"Submit"按钮保存
7. 验证翻译已成功添加
```

### **3.2 批量翻译操作策略**

#### **方法A: 逐语言添加 (推荐)**
- 为单个服务依次添加所有10种语言
- 优点: 集中处理，减少切换成本
- 适用: 翻译内容已准备完整的情况

#### **方法B: 逐服务添加**
- 为所有服务添加同一种语言，然后切换到下一种语言
- 优点: 语言切换少，翻译一致性好
- 适用: 多个服务使用相似翻译模板的情况

### **3.3 完全自动化JavaScript脚本应用 (推荐)**

#### **🚀 脚本自动化优势**
基于KTMB项目实际验证，JavaScript脚本自动化可实现95%+效率提升：

**传统手动操作 vs 完全自动化对比**:
| 操作步骤 | 手动操作时间 | 自动化时间 | 效率提升 |
|----------|-------------|------------|----------|
| Add Translate点击 | 10-15秒 | 即时 | 95%+ |
| 语言选择 | 10-15秒 | 即时 | 95%+ |
| 内容填写 | 75-105秒 | 即时 | 95%+ |
| Submit提交 | 5-10秒 | 即时 | 90%+ |
| **总体操作** | **100-145秒** | **5-10秒** | **95%+** |

#### **🔧 脚本应用最佳实践**

**混合操作模式 (推荐)**:
1. **手动导航**: 项目选择、子项目管理界面导航
2. **脚本自动化**: Add Translate按钮点击、表单填写、提交
3. **手动验证**: 翻译状态确认、质量检查

**脚本使用步骤**:
```javascript
// 1. 注入完全自动化脚本
chrome_inject_script_chrome-mcp-stdio

// 2. 使用批量添加函数 (示例)
batchAddTranslations(['zh-cn', 'zh-tw', 'ms', 'id', 'ja', 'ko', 'th', 'vi', 'ru'])

// 3. 验证翻译状态
chrome_get_web_content_chrome-mcp-stdio selector="#qrCodeSubTranslateTable_info"
```

#### **⚠️ 脚本应用注意事项**

**关键技术细节**:
- **Add Translate按钮选择器**: `button.btn.btn-outline-primary[onclick*="qrCodeSubTranslateEditModalOpen('create'"]`
- **Submit按钮选择器**: `button.btn.btn-primary[onclick*="submit_qrCodeSubTranslate_form()"]`
- **等待时间设置**: 每次操作间隔2-3秒，确保页面稳定
- **错误处理**: 包含按钮未找到、表单加载失败的备用方案

**常见问题解决**:
- **脚本执行中断**: 检查页面是否刷新，重新注入脚本
- **按钮点击失败**: 使用备用选择器或手动点击
- **表单填写失败**: 检查元素ID是否正确，等待页面加载完成
- **批量操作卡顿**: 适当增加操作间隔时间

#### **📊 批量翻译操作经验**

**KTMB项目实际验证数据**:
- **Kuala Selangor Private Charter**: 9种语言批量添加，成功率100%
- **Hourly Charter 3 Hour**: 10种语言批量添加，成功率100%
- **平均单个翻译时间**: 15-30秒 (包含等待时间)
- **总体效率提升**: 从2小时手动操作缩短至30分钟

**批量操作最佳实践**:
1. **分批处理**: 每批3-5种语言，避免系统负载过高
2. **实时监控**: 每完成一个翻译立即验证状态
3. **错误恢复**: 遇到失败立即停止，手动处理后继续
4. **进度记录**: 在项目文档中实时更新完成状态

### **3.4 错误处理与重试**
- **元素不可见**: 刷新页面后重试
- **提交失败**: 检查Description和Remark是否都已填写
- **翻译重复**: 检查语言选择是否正确
- **脚本执行失败**: 重新注入脚本，检查选择器是否正确

---

## 📊 **阶段4: 质量控制与验证**

### **4.1 实时验证**
每添加一个翻译后立即验证：
- [ ] 翻译条目计数增加 (如: 1 to 1 of 1 entries)
- [ ] 翻译内容显示正确
- [ ] 语言标识准确

### **4.2 服务级逐一验证 (严格标准)**

#### **4.2.1 翻译完整性逐一确认**
每完成一个服务的所有翻译后，必须逐一验证每种语言：

**标准10语言逐一验证清单**:
- [ ] **English** (en) - 翻译条目存在且内容完整
- [ ] **简体中文** (zh-cn) - 翻译条目存在且内容完整
- [ ] **繁體中文** (zh-tw) - 翻译条目存在且内容完整
- [ ] **Bahasa Melayu** (ms) - 翻译条目存在且内容完整
- [ ] **Bahasa Indonesia** (id) - 翻译条目存在且内容完整
- [ ] **日本語** (ja) - 翻译条目存在且内容完整
- [ ] **한국어** (ko) - 翻译条目存在且内容完整
- [ ] **ภาษาไทย** (th) - 翻译条目存在且内容完整
- [ ] **Tiếng Việt** (vi) - 翻译条目存在且内容完整
- [ ] **Русский** (ru) - 翻译条目存在且内容完整

#### **4.2.2 翻译内容质量逐一检查**
对每种语言的翻译内容进行详细检查：

**内容完整性检查标准**:
```bash
# 对每种语言执行以下检查
1. 点击对应语言的"Edit"按钮
2. 验证Description字段:
   - [ ] 内容非空
   - [ ] 语言正确
   - [ ] 描述准确
   - [ ] 格式规范
3. 验证Remark字段:
   - [ ] 内容非空
   - [ ] 语言正确
   - [ ] 信息完整
   - [ ] 格式一致
4. 关闭编辑窗口
```

#### **4.2.3 翻译状态数值验证**
- [ ] 翻译管理界面显示"Showing 1 to 10 of 10 entries"
- [ ] 翻译状态计数器显示"10/10"
- [ ] 无重复语言条目
- [ ] 无空白翻译条目

#### **4.2.4 服务验证记录模板**
在项目文档中记录验证结果：
```markdown
### **{服务名称} 翻译验证记录**
**验证时间**: {YYYY-MM-DD HH:MM}
**验证人员**: {验证人员}
**翻译状态**: ✅ 完整翻译 (10/10)

**语言验证详情**:
| 语言 | 代码 | Description | Remark | 状态 |
|------|------|-------------|--------|------|
| English | en | ✅ 完整 | ✅ 完整 | ✅ 通过 |
| 简体中文 | zh-cn | ✅ 完整 | ✅ 完整 | ✅ 通过 |
| 繁體中文 | zh-tw | ✅ 完整 | ✅ 完整 | ✅ 通过 |
| Bahasa Melayu | ms | ✅ 完整 | ✅ 完整 | ✅ 通过 |
| Bahasa Indonesia | id | ✅ 完整 | ✅ 完整 | ✅ 通过 |
| 日本語 | ja | ✅ 完整 | ✅ 完整 | ✅ 通过 |
| 한국어 | ko | ✅ 完整 | ✅ 完整 | ✅ 通过 |
| ภาษาไทย | th | ✅ 完整 | ✅ 完整 | ✅ 通过 |
| Tiếng Việt | vi | ✅ 完整 | ✅ 完整 | ✅ 通过 |
| Русский | ru | ✅ 完整 | ✅ 完整 | ✅ 通过 |

**质量问题**: 无
**修正记录**: 无
**验证结论**: ✅ 通过验证
```

### **4.3 翻译质量问题追踪与修正**

#### **4.3.1 质量问题分类**
- **A级问题**: 翻译缺失或语言错误
- **B级问题**: 翻译内容不准确或不完整
- **C级问题**: 格式不规范或表述不一致

#### **4.3.2 问题追踪记录**
```markdown
### **翻译质量问题追踪**
| 问题ID | 服务名称 | 语言 | 问题类型 | 问题描述 | 修正状态 | 修正时间 |
|--------|----------|------|----------|----------|----------|----------|
| Q001 | {服务名} | zh-cn | A级 | Description字段为空 | ✅ 已修正 | 2025-01-27 |
| Q002 | {服务名} | ja | B级 | 时间描述不准确 | ✅ 已修正 | 2025-01-27 |
```

#### **4.3.3 修正验证流程**
1. **问题识别**: 在逐一验证过程中发现问题
2. **问题记录**: 在追踪表中记录问题详情
3. **立即修正**: 停止后续验证，优先修正当前问题
4. **重新验证**: 修正后重新执行该服务的完整验证
5. **状态更新**: 更新问题追踪记录和验证状态

### **4.4 项目级综合验证**
完成所有服务的逐一验证后：

#### **4.4.1 项目整体状态验证**
- [ ] 重新分析项目翻译状态
- [ ] 验证所有服务都达到10/10状态
- [ ] 确认项目整体翻译完整度为100%
- [ ] 检查无遗漏的子项目

#### **4.4.2 最终质量报告生成**
```markdown
## 🎯 **项目翻译质量验证报告**

**项目名称**: {项目名称}
**验证时间**: {YYYY-MM-DD}
**验证标准**: 逐一验证标准v2.0

### **验证结果汇总**
- **子项目总数**: {数量}个
- **翻译完整度**: 100% (60/60个翻译条目)
- **质量问题数**: {数量}个 (已全部修正)
- **验证状态**: ✅ 全部通过

### **服务验证明细**
| 序号 | 服务名称 | 翻译状态 | 验证状态 | 质量问题 |
|------|----------|----------|----------|----------|
| 1 | {服务1} | 10/10 | ✅ 通过 | 0个 |
| 2 | {服务2} | 10/10 | ✅ 通过 | 0个 |
| ... | ... | ... | ... | ... |

### **验证结论**
✅ 项目翻译补充工作质量验证通过，所有翻译条目均达到标准要求。
```

#### **4.4.3 文档更新与归档**
- [ ] 更新项目主文档最终状态
- [ ] 生成完整的验证记录
- [ ] 归档质量问题追踪记录
- [ ] 创建项目完成总结

---

## ⏱️ **时间估算标准 (基于逐一验证标准)**

### **单个翻译条目**
- **数据准备**: 1-2分钟
- **系统录入**: 2-3分钟 (手动) / 15-30秒 (脚本自动化)
- **实时验证**: 30秒
- **小计**: 4-6分钟/条目 (手动) / 2-3分钟/条目 (自动化)

### **单个服务 (10种语言)**
- **翻译添加**: 30-40分钟 (已有数据) / 45-60分钟 (需创建)
- **逐一验证**: 15-20分钟 (新增严格标准)
- **验证记录**: 5分钟
- **问题修正**: 0-10分钟 (如有问题)
- **小计**: 50-75分钟/服务

### **逐一验证时间明细**
- **翻译完整性确认**: 5分钟 (10种语言逐一检查)
- **内容质量检查**: 8-10分钟 (每种语言Edit查看)
- **状态数值验证**: 2分钟
- **验证记录填写**: 5分钟
- **小计**: 20-22分钟/服务

### **项目级估算公式 (更新)**
```
翻译添加时间 = (无翻译服务数 × 50分钟) + (部分翻译服务数 × 缺失语言数 × 3分钟)
逐一验证时间 = 服务总数 × 20分钟
项目综合验证 = 20分钟
总时间 = 翻译添加时间 + 逐一验证时间 + 项目综合验证
```

### **KTMB项目实际时间对比**
| 阶段 | 抽查验证模式 | 逐一验证模式 | 时间增加 |
|------|-------------|-------------|----------|
| **翻译添加** | 90分钟 | 90分钟 | 0% |
| **质量验证** | 15分钟 | 120分钟 | +700% |
| **总时间** | 105分钟 | 210分钟 | +100% |
| **质量保证** | 85% | 99.9% | +17% |

---

## 📋 **工作清单模板**

### **项目开始前**
- [ ] 创建项目主文档: `{项目名称}_QR_Code_Complete_Report.md`
- [ ] 完成项目分析和翻译状态评估
- [ ] 准备所需翻译内容数据
- [ ] 制定翻译补充计划

### **翻译执行中**
- [ ] 按优先级顺序处理服务
- [ ] 每完成一个服务立即执行逐一验证
- [ ] 记录每个服务的详细验证结果
- [ ] 追踪和修正发现的质量问题
- [ ] 更新项目文档进展状态
- [ ] 定期保存工作进展

### **逐一验证执行清单**
每完成一个服务后必须执行：
- [ ] **翻译完整性确认**: 逐一检查10种语言是否存在
- [ ] **内容质量检查**: 逐一查看每种语言的Description和Remark
- [ ] **状态数值验证**: 确认显示"10/10"和"10 entries"
- [ ] **验证记录填写**: 在项目文档中记录详细验证结果
- [ ] **问题追踪处理**: 发现问题立即记录并修正
- [ ] **重新验证确认**: 修正问题后重新执行完整验证

### **项目完成后**
- [ ] 执行项目级综合验证
- [ ] 生成最终质量验证报告
- [ ] 确认所有服务都通过逐一验证
- [ ] 更新项目文档最终状态
- [ ] 归档验证记录和质量追踪文件
- [ ] 生成项目完成总结

---

## 🚨 **常见问题与解决方案**

### **问题1: 页面元素找不到**
**解决方案**: 刷新页面，等待完全加载后重试

### **问题2: 翻译提交失败**
**解决方案**: 检查Description和Remark是否都已填写

### **问题3: 语言选择错误**
**解决方案**: 删除错误翻译，重新添加正确语言

### **问题4: 翻译内容过长**
**解决方案**: 适当精简内容，保留核心信息

### **问题5: JavaScript脚本注入失败 (新增)**
**现象**: 脚本注入后无响应或控制台无输出
**解决方案**:
- 检查页面是否完全加载
- 确认脚本类型设置为"MAIN"
- 重新刷新页面后重试注入

### **问题6: Add Translate按钮自动点击失败 (新增)**
**现象**: 脚本找不到Add Translate按钮或点击无效
**解决方案**:
- 使用备用选择器: `button.btn.btn-outline-primary`
- 通过文本内容查找: `btn.textContent.trim() === 'Add Translate'`
- 手动点击后继续脚本自动化流程

### **问题7: 批量翻译中途中断 (新增)**
**现象**: 批量脚本执行到一半停止
**解决方案**:
- 检查当前翻译状态，记录已完成的语言
- 从中断点继续执行剩余语言
- 适当增加操作间隔时间 (3-5秒)

### **问题8: 翻译模态窗口加载缓慢 (新增)**
**现象**: 点击Add Translate后表单加载时间过长
**解决方案**:
- 增加等待时间至2-3秒
- 使用`waitForElement()`函数等待元素出现
- 检查网络连接和系统负载

### **问题9: 子项目Translate按钮定位困难 (新增)**
**现象**: 多个子项目时难以准确点击目标服务的Translate按钮
**解决方案**:
- 使用精确的sub_id选择器: `button[onclick*="qrCodeSubTranslate('sub_id')"]`
- 直接调用JavaScript函数: `qrCodeSubTranslate('sub_id')`
- 滚动页面确保按钮可见

### **问题10: 翻译内容创建困难 (新增)**
**现象**: 新服务类型缺少翻译模板
**解决方案**:
- 参考同类服务的翻译内容
- 基于现有模板调整时长、服务范围等关键信息
- 确保Description和Remark结构一致

---

## 📈 **持续改进**

### **流程优化建议**
1. **收集反馈**: 记录每次使用流程的问题和建议
2. **更新标准**: 定期更新翻译质量标准和操作步骤
3. **工具改进**: 探索自动化工具减少手动操作
4. **培训材料**: 基于实际经验完善培训文档

### **版本控制**
- **v1.0**: 基于KTMB项目经验的初始版本
- **v2.0**: 新增完全自动化JavaScript脚本应用经验，包含：
  - 完全自动化脚本的实际应用效果和优化建议
  - 批量翻译操作中的技术问题和解决方案
  - Add Translate按钮自动化集成的技术细节
  - 混合操作模式的最佳实践
  - 基于KTMB项目验证的6个新问题和解决方案
- **v2.1**: 质量控制标准重大升级，包含：
  - 将抽查验证升级为逐一验证标准
  - 新增10种语言的逐一确认清单
  - 建立翻译内容质量的详细检查标准
  - 创建质量问题追踪与修正机制
  - 更新时间估算标准（反映逐一验证成本）
  - 完善验证记录模板和质量报告格式
- **后续版本**: 根据更多项目实践持续优化

---

**文档维护**: 每完成一个QR Code项目翻译补充工作后，更新本流程文档  
**适用范围**: 所有GoMyHire QR码管理系统的翻译补充工作  
**更新频率**: 根据实际使用反馈定期更新
